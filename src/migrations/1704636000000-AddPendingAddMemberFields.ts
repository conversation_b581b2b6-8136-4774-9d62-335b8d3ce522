import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddPendingAddMemberFields1704636000000 implements MigrationInterface {
  name = 'AddPendingAddMemberFields1704636000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add pendingAddMemberCharges column
    await queryRunner.addColumn(
      'company_subscriptions',
      new TableColumn({
        name: 'pendingAddMemberCharges',
        type: 'decimal',
        precision: 10,
        scale: 2,
        default: 0,
        isNullable: false
      })
    );

    // Add pendingAddMemberDetails column
    await queryRunner.addColumn(
      'company_subscriptions',
      new TableColumn({
        name: 'pendingAddMemberDetails',
        type: 'text',
        isNullable: true
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the columns in reverse order
    await queryRunner.dropColumn('company_subscriptions', 'pendingAddMemberDetails');
    await queryRunner.dropColumn('company_subscriptions', 'pendingAddMemberCharges');
  }
}
