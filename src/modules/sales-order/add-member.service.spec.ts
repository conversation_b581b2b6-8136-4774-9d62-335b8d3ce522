import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Logger } from '@nestjs/common';
import { SalesOrderService } from './sales-order.service';
import { SalesOrderEntity } from './entity/sales-order.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { CompanySubscriptionEntity } from '@modules/company-subscription/entity/company-subscription.entity';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { SalesOrderStatus } from '@constants';
import { SenangPayService } from '@modules/integration/senangpay/senangpay.service';
import { BillplzService } from '@modules/integration/billplz/billplz.service';
import { SubscriptionPackageService } from '@modules/subscription-package/subscription-package.service';

// Mock the dependencies
jest.mock('@paralleldrive/cuid2', () => ({
  createId: jest.fn().mockReturnValue('mock-cuid')
}));

jest.mock('@common/common-helper', () => ({
  generateInvoiceNumber: jest.fn().mockReturnValue('INV-12345')
}));

jest.mock('@common/pricing-helper', () => ({
  calculateSubscriptionPrice: jest.fn().mockReturnValue({
    firstPeriod: {
      totalAmount: 100,
      baseAmount: 94.34,
      sstAmount: 5.66,
      totalAmountInCents: 10000,
      baseAmountInCents: 9434,
      sstAmountInCents: 566,
      isProrated: true
    },
    fullMonthPeriod: {
      totalAmount: 200,
      baseAmount: 188.68,
      sstAmount: 11.32,
      totalAmountInCents: 20000,
      baseAmountInCents: 18868,
      sstAmountInCents: 1132
    },
    combined: {
      totalAmount: 300,
      baseAmount: 283.02,
      sstAmount: 16.98,
      totalAmountInCents: 30000,
      baseAmountInCents: 28302,
      sstAmountInCents: 1698
    }
  })
}));

// Mock TypeORM functions
const mockUserRepo = {
  findOne: jest.fn()
};

const mockSubscriptionRepo = {
  findOne: jest.fn()
};

jest.mock('typeorm', () => {
  const actual = jest.requireActual('typeorm');
  return {
    ...actual,
    getRepository: jest.fn((entity) => {
      if (entity.name === 'UserEntity') return mockUserRepo;
      if (entity.name === 'CompanySubscriptionEntity') return mockSubscriptionRepo;
      return {};
    }),
    getManager: jest.fn(() => ({
      transaction: jest.fn()
    }))
  };
});

describe('SalesOrderService - Add Member Functionality', () => {
  let service: SalesOrderService;
  let moduleRef: TestingModule;

  // Mock entities
  const mockUser = {
    id: 1,
    email: '<EMAIL>',
    name: 'Test User',
    companyId: 1
  };

  const mockYearlySubscription = {
    id: 1,
    companyId: 1,
    subscriptionPackageId: 1,
    seatCount: 5,
    isYearly: true,
    nextBillingDate: new Date('2024-01-27'),
    subscriptionPackage: {
      id: 1,
      title: 'Basic',
      amount: 100
    },
    company: {
      id: 1,
      maxUsers: 5
    }
  };

  const mockMonthlySubscription = {
    id: 2,
    companyId: 1,
    subscriptionPackageId: 1,
    seatCount: 5,
    isYearly: false,
    nextBillingDate: new Date('2024-01-27'),
    subscriptionPackage: {
      id: 1,
      title: 'Basic',
      amount: 100
    },
    company: {
      id: 1,
      maxUsers: 5
    }
  };

  // Mock repositories
  const mockEntityManager = {
    transaction: jest.fn(),
    getRepository: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    update: jest.fn()
  };

  const mockBillplzService = {
    createBill: jest.fn().mockResolvedValue({
      id: 'bill-123',
      url: 'https://billplz.com/bills/bill-123'
    })
  };

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup transaction mock
    mockEntityManager.transaction.mockImplementation(async (callback) => {
      return await callback(mockEntityManager);
    });

    moduleRef = await Test.createTestingModule({
      providers: [
        SalesOrderService,
        {
          provide: getRepositoryToken(SalesOrderEntity),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            find: jest.fn().mockResolvedValue([])
          }
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockResolvedValue(mockUser)
          }
        },
        {
          provide: getRepositoryToken(CompanySubscriptionEntity),
          useValue: {
            findOne: jest.fn()
          }
        },
        {
          provide: getRepositoryToken(SubscriptionPackageEntity),
          useValue: {}
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {}
        },
        {
          provide: SenangPayService,
          useValue: {}
        },
        {
          provide: BillplzService,
          useValue: mockBillplzService
        },
        {
          provide: SubscriptionPackageService,
          useValue: {}
        }
      ],
    }).compile();

    service = moduleRef.get<SalesOrderService>(SalesOrderService);
  });

  afterEach(async () => {
    if (moduleRef) {
      await moduleRef.close();
    }
  });

  describe('createAddMemberForm', () => {
    it('should create immediate payment for yearly subscription', async () => {
      // Setup mocks
      mockUserRepo.findOne.mockResolvedValue(mockUser);
      mockSubscriptionRepo.findOne.mockResolvedValue(mockYearlySubscription);

      mockEntityManager.transaction.mockImplementation(async (callback) => {
        return await callback(mockEntityManager);
      });

      mockEntityManager.getRepository.mockReturnValue({
        find: jest.fn().mockResolvedValue([]),
        save: jest.fn().mockResolvedValue({
          id: 1,
          cuid: 'mock-cuid',
          total: 100
        }),
        update: jest.fn()
      });

      // Test
      const result = await service.createAddMemberForm(1, 1, 8);

      // Assertions
      expect(result.success).toBe(true);
      expect(result.requiresImmediatePayment).toBe(true);
      expect(result.paymentUrl).toBe('https://billplz.com/bills/bill-123');
      expect(result.totalAmount).toBe(100);
      expect(mockBillplzService.createBill).toHaveBeenCalled();
    });

    it('should create deferred payment for monthly subscription', async () => {
      // Setup mocks
      const userRepo = moduleRef.get(getRepositoryToken(UserEntity));
      const subscriptionRepo = moduleRef.get(getRepositoryToken(CompanySubscriptionEntity));

      userRepo.findOne = jest.fn().mockResolvedValue(mockUser);
      subscriptionRepo.findOne = jest.fn().mockResolvedValue(mockMonthlySubscription);

      mockEntityManager.getRepository.mockReturnValue({
        find: jest.fn().mockResolvedValue([]),
        save: jest.fn().mockResolvedValue({
          id: 1,
          cuid: 'mock-cuid',
          total: 100
        }),
        update: jest.fn()
      });

      // Test
      const result = await service.createAddMemberForm(1, 2, 8);

      // Assertions
      expect(result.success).toBe(true);
      expect(result.requiresImmediatePayment).toBe(false);
      expect(result.paymentUrl).toBeUndefined();
      expect(result.totalAmount).toBe(100);
      expect(mockBillplzService.createBill).not.toHaveBeenCalled();
      expect(mockEntityManager.update).toHaveBeenCalled(); // Subscription updated immediately
    });

    it('should return error for invalid subscription', async () => {
      // Setup mocks
      const userRepo = moduleRef.get(getRepositoryToken(UserEntity));
      const subscriptionRepo = moduleRef.get(getRepositoryToken(CompanySubscriptionEntity));

      userRepo.findOne = jest.fn().mockResolvedValue(mockUser);
      subscriptionRepo.findOne = jest.fn().mockResolvedValue(null);

      // Test
      const result = await service.createAddMemberForm(1, 999, 8);

      // Assertions
      expect(result.success).toBe(false);
      expect(result.message).toBe('Subscription not found');
    });

    it('should return error when new seat count is not greater than current', async () => {
      // Setup mocks
      const userRepo = moduleRef.get(getRepositoryToken(UserEntity));
      const subscriptionRepo = moduleRef.get(getRepositoryToken(CompanySubscriptionEntity));

      userRepo.findOne = jest.fn().mockResolvedValue(mockUser);
      subscriptionRepo.findOne = jest.fn().mockResolvedValue(mockYearlySubscription);

      // Test - trying to set same seat count
      const result = await service.createAddMemberForm(1, 1, 5);

      // Assertions
      expect(result.success).toBe(false);
      expect(result.message).toBe('New seat count must be greater than current seat count');
    });

    it('should cancel existing pending add member orders', async () => {
      // Setup mocks
      const userRepo = moduleRef.get(getRepositoryToken(UserEntity));
      const subscriptionRepo = moduleRef.get(getRepositoryToken(CompanySubscriptionEntity));

      userRepo.findOne = jest.fn().mockResolvedValue(mockUser);
      subscriptionRepo.findOne = jest.fn().mockResolvedValue(mockYearlySubscription);

      const mockSalesOrderRepo = {
        find: jest.fn().mockResolvedValue([
          { id: 1, status: SalesOrderStatus.Pending },
          { id: 2, status: SalesOrderStatus.Pending }
        ]),
        save: jest.fn().mockResolvedValue({
          id: 3,
          cuid: 'mock-cuid',
          total: 100
        }),
        update: jest.fn()
      };

      mockEntityManager.getRepository.mockReturnValue(mockSalesOrderRepo);

      // Test
      await service.createAddMemberForm(1, 1, 8);

      // Assertions
      expect(mockSalesOrderRepo.update).toHaveBeenCalledWith(
        [1, 2],
        { status: SalesOrderStatus.Cancelled }
      );
    });
  });
});
