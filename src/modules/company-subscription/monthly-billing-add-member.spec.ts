import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CompanySubscriptionService } from './company-subscription.service';
import { CompanySubscriptionEntity } from './entity/company-subscription.entity';
import { SalesOrderEntity } from '@modules/sales-order/entity/sales-order.entity';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';

// Mock external dependencies
jest.mock('@paralleldrive/cuid2', () => ({
  createId: jest.fn().mockReturnValue('billing-order-123')
}));

jest.mock('@common/common-helper', () => ({
  generateInvoiceNumber: jest.fn().mockReturnValue('INV-BILLING-001')
}));

jest.mock('@common/pricing-helper', () => ({
  calculateSubscriptionPrice: jest.fn().mockReturnValue({
    fullMonthPeriod: {
      totalAmountInCents: 40000, // RM 400 for 8 seats
      baseAmountInCents: 37736,
      sstAmountInCents: 2264
    }
  })
}));

describe('CompanySubscriptionService - Monthly Billing with Add Member', () => {
  let service: CompanySubscriptionService;
  let moduleRef: TestingModule;

  const mockSubscriptionWithAddMember = {
    id: 1,
    companyId: 1,
    subscriptionPackageId: 1,
    seatCount: 8, // Increased from 5 to 8
    isYearly: false,
    nextBillingDate: new Date('2024-01-27'),
    creditBalance: 0,
    pendingAddMemberCharges: 60.00, // RM 60 for 3 added seats (prorated)
    pendingAddMemberDetails: JSON.stringify([
      {
        addedAt: '2024-01-15T10:00:00.000Z',
        addedSeats: 3,
        previousSeatCount: 5,
        newSeatCount: 8,
        proratedAmount: 60.00,
        billingPeriod: {
          startDate: '2024-01-15',
          endDate: '2024-01-27',
          daysProrated: 12
        },
        pricing: {
          baseAmount: 56.60,
          sstAmount: 3.40,
          totalAmount: 60.00
        }
      }
    ]),
    subscriptionPackage: {
      id: 1,
      title: 'Basic',
      amount: 50
    },
    company: {
      id: 1,
      name: 'Test Company',
      ownerId: 1
    }
  };

  const mockEntityManager = {
    transaction: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    update: jest.fn()
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    // Setup transaction mock
    mockEntityManager.transaction.mockImplementation(async (callback) => {
      return await callback(mockEntityManager);
    });

    moduleRef = await Test.createTestingModule({
      providers: [
        CompanySubscriptionService,
        {
          provide: getRepositoryToken(CompanySubscriptionEntity),
          useValue: {
            createQueryBuilder: jest.fn().mockReturnValue({
              innerJoinAndSelect: jest.fn().mockReturnThis(),
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              getMany: jest.fn().mockResolvedValue([mockSubscriptionWithAddMember])
            })
          }
        },
        {
          provide: getRepositoryToken(SalesOrderEntity),
          useValue: {}
        },
        {
          provide: getRepositoryToken(SubscriptionPackageEntity),
          useValue: {}
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {}
        }
      ],
    }).compile();

    service = moduleRef.get<CompanySubscriptionService>(CompanySubscriptionService);

    // Mock getManager
    const typeormModule = jest.requireActual('typeorm');
    jest.doMock('typeorm', () => ({
      ...typeormModule,
      getManager: () => mockEntityManager
    }));
  });

  afterEach(async () => {
    if (moduleRef) {
      await moduleRef.close();
    }
  });

  describe('handleMonthlyBilling with Add Member Charges', () => {
    it('should include pending add member charges in monthly billing', async () => {
      // Setup mocks
      mockEntityManager.findOne.mockResolvedValue(null); // No existing pending sales order
      mockEntityManager.save.mockResolvedValue({
        id: 1,
        cuid: 'billing-order-123',
        total: 460.00 // RM 400 (subscription) + RM 60 (add member)
      });

      // Test
      await service.handleMonthlyBilling();

      // Verify sales order was created with combined amount
      expect(mockEntityManager.save).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 1,
          cuid: 'billing-order-123',
          companyId: 1,
          subscriptionPackageId: 1,
          total: 460.00, // Combined amount: RM 400 + RM 60
          invoiceNumber: 'INV-BILLING-001',
          status: 'pending',
          isRecurrence: true,
          data: expect.stringContaining('"subscriptionAmount":400')
        })
      );

      // Verify the data field contains add member details
      const saveCall = mockEntityManager.save.mock.calls[0][0];
      const orderData = JSON.parse(saveCall.data);

      expect(orderData.subscriptionAmount).toBe(400);
      expect(orderData.pendingAddMemberCharges).toBe(60);
      expect(orderData.totalBillingAmount).toBe(460);
      expect(orderData.addMemberDetails).toHaveLength(1);
      expect(orderData.addMemberDetails[0].addedSeats).toBe(3);
      expect(orderData.addMemberDetails[0].proratedAmount).toBe(60);

      // Verify subscription was updated to clear pending charges
      expect(mockEntityManager.update).toHaveBeenCalledWith(
        CompanySubscriptionEntity,
        { id: 1 },
        expect.objectContaining({
          pendingAddMemberCharges: 0,
          pendingAddMemberDetails: null
        })
      );
    });

    it('should handle subscription with no pending add member charges', async () => {
      // Setup subscription without pending charges
      const subscriptionWithoutAddMember = {
        ...mockSubscriptionWithAddMember,
        pendingAddMemberCharges: 0,
        pendingAddMemberDetails: null
      };

      const mockRepo = moduleRef.get(getRepositoryToken(CompanySubscriptionEntity));
      mockRepo.createQueryBuilder().getMany.mockResolvedValue([subscriptionWithoutAddMember]);

      mockEntityManager.findOne.mockResolvedValue(null);
      mockEntityManager.save.mockResolvedValue({
        id: 1,
        cuid: 'billing-order-123',
        total: 400.00 // Only subscription amount
      });

      // Test
      await service.handleMonthlyBilling();

      // Verify sales order was created with subscription amount only
      expect(mockEntityManager.save).toHaveBeenCalledWith(
        expect.objectContaining({
          total: 400.00 // Only subscription amount
        })
      );

      // Verify the data field shows no add member charges
      const saveCall = mockEntityManager.save.mock.calls[0][0];
      const orderData = JSON.parse(saveCall.data);

      expect(orderData.subscriptionAmount).toBe(400);
      expect(orderData.pendingAddMemberCharges).toBe(0);
      expect(orderData.totalBillingAmount).toBe(400);
      expect(orderData.addMemberDetails).toEqual([]);
    });

    it('should apply credit balance to combined billing amount', async () => {
      // Setup subscription with credit balance
      const subscriptionWithCredit = {
        ...mockSubscriptionWithAddMember,
        creditBalance: 100.00 // RM 100 credit
      };

      const mockRepo = moduleRef.get(getRepositoryToken(CompanySubscriptionEntity));
      mockRepo.createQueryBuilder().getMany.mockResolvedValue([subscriptionWithCredit]);

      mockEntityManager.findOne.mockResolvedValue(null);
      mockEntityManager.save.mockResolvedValue({
        id: 1,
        cuid: 'billing-order-123',
        total: 360.00 // RM 460 - RM 100 credit
      });

      // Test
      await service.handleMonthlyBilling();

      // Verify credit was applied to combined amount
      const saveCall = mockEntityManager.save.mock.calls[0][0];
      const orderData = JSON.parse(saveCall.data);

      expect(orderData.totalBillingAmount).toBe(460); // Before credit
      expect(orderData.creditApplied).toBe(100);
      expect(orderData.finalAmount).toBe(360); // After credit
      expect(saveCall.total).toBe(360);

      // Verify credit balance was reduced
      expect(mockEntityManager.update).toHaveBeenCalledWith(
        CompanySubscriptionEntity,
        { id: 1 },
        expect.objectContaining({
          creditBalance: 0, // 100 - 100 = 0
          pendingAddMemberCharges: 0,
          pendingAddMemberDetails: null
        })
      );
    });
  });
});
